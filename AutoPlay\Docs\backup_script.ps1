# سكريبت النسخ الاحتياطية التلقائية لقاعدة بيانات الجيم
# Automatic Backup Script for GYM Database

param(
    [string]$SourcePath = "AutoPlay\Docs\GYM.accdb",
    [string]$BackupFolder = "AutoPlay\Docs\Backups"
)

# إنشاء مجلد النسخ الاحتياطية
if (-not (Test-Path $BackupFolder)) {
    New-Item -ItemType Directory -Path $BackupFolder -Force
    Write-Host "✅ تم إنشاء مجلد النسخ الاحتياطية: $BackupFolder"
}

# التحقق من وجود الملف الأساسي
if (-not (Test-Path $SourcePath)) {
    Write-Host "❌ خطأ: لم يتم العثور على ملف قاعدة البيانات: $SourcePath"
    exit 1
}

# إنشاء timestamp
$timestamp = Get-Date -Format "yyyy-MM-dd_HH-mm-ss"
$date = Get-Date -Format "yyyy-MM-dd"

try {
    # نسخة احتياطية يومية
    $dailyBackup = "$BackupFolder\GYM_Daily_$timestamp.accdb"
    Copy-Item $SourcePath $dailyBackup -Force
    Write-Host "✅ تم إنشاء النسخة الاحتياطية اليومية: $dailyBackup"
    
    # نسخة احتياطية أسبوعية (كل جمعة)
    if ((Get-Date).DayOfWeek -eq "Friday") {
        $weeklyBackup = "$BackupFolder\GYM_Weekly_$timestamp.accdb"
        Copy-Item $SourcePath $weeklyBackup -Force
        Write-Host "✅ تم إنشاء النسخة الاحتياطية الأسبوعية: $weeklyBackup"
    }
    
    # نسخة احتياطية شهرية (أول يوم في الشهر)
    if ((Get-Date).Day -eq 1) {
        $monthlyBackup = "$BackupFolder\GYM_Monthly_$timestamp.accdb"
        Copy-Item $SourcePath $monthlyBackup -Force
        Write-Host "✅ تم إنشاء النسخة الاحتياطية الشهرية: $monthlyBackup"
    }
    
    # تحديث النسخة الاحتياطية الرئيسية
    $mainBackup = "AutoPlay\Docs\GYM_Backup.accdb"
    Copy-Item $SourcePath $mainBackup -Force
    Write-Host "✅ تم تحديث النسخة الاحتياطية الرئيسية"
    
    # حذف النسخ القديمة (أكثر من 30 يوم)
    $oldBackups = Get-ChildItem "$BackupFolder\GYM_Daily_*.accdb" | Where-Object { $_.LastWriteTime -lt (Get-Date).AddDays(-30) }
    foreach ($oldBackup in $oldBackups) {
        Remove-Item $oldBackup.FullName -Force
        Write-Host "🗑️ تم حذف النسخة القديمة: $($oldBackup.Name)"
    }
    
    # عرض معلومات النسخ الاحتياطية
    Write-Host "`n=== معلومات النسخ الاحتياطية ==="
    $sourceFile = Get-Item $SourcePath
    Write-Host "الملف الأساسي: $([math]::Round($sourceFile.Length/1MB, 2)) MB - آخر تعديل: $($sourceFile.LastWriteTime)"
    
    Write-Host "`nالنسخ الاحتياطية المتاحة:"
    Get-ChildItem "$BackupFolder\*.accdb" | Sort-Object LastWriteTime -Descending | Select-Object -First 10 | ForEach-Object {
        Write-Host "  $($_.Name) - $([math]::Round($_.Length/1MB, 2)) MB - $($_.LastWriteTime)"
    }
    
    Write-Host "`n✅ تم إكمال عملية النسخ الاحتياطي بنجاح"
    
} catch {
    Write-Host "Error in backup process: $($_.Exception.Message)"
    exit 1
}
