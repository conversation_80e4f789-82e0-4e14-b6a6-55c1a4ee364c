@echo off
echo ========================================
echo        نسخ احتياطي لقاعدة بيانات الجيم
echo ========================================
echo.

REM إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجود
if not exist "Backups" mkdir Backups

REM إنشاء اسم الملف بالتاريخ والوقت
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "timestamp=%YYYY%-%MM%-%DD%_%HH%-%Min%"

REM نسخ الملف الأساسي
echo جاري إنشاء نسخة احتياطية...
copy "GYM.accdb" "Backups\GYM_Backup_%timestamp%.accdb" >nul
if %errorlevel% equ 0 (
    echo ✓ تم إنشاء النسخة الاحتياطية بنجاح
    echo   الملف: Backups\GYM_Backup_%timestamp%.accdb
) else (
    echo ✗ فشل في إنشاء النسخة الاحتياطية
    goto end
)

REM تحديث النسخة الاحتياطية الرئيسية
copy "GYM.accdb" "GYM_Backup.accdb" >nul
if %errorlevel% equ 0 (
    echo ✓ تم تحديث النسخة الاحتياطية الرئيسية
) else (
    echo ✗ فشل في تحديث النسخة الاحتياطية الرئيسية
)

REM كتابة سجل العمليات
echo %date% %time% - تم إنشاء نسخة احتياطية: GYM_Backup_%timestamp%.accdb >> Backups\backup_log.txt

REM عرض النسخ الاحتياطية المتاحة
echo.
echo النسخ الاحتياطية المتاحة:
echo ========================
dir Backups\GYM_*.accdb /b /o-d 2>nul | findstr /n "^" | findstr "^[1-5]:"

:end
echo.
echo اضغط أي مفتاح للخروج...
pause >nul
