﻿# سكريبت النسخ الاحتياطي اليومي
$sourceFile = "D:\Desktop\STYLE 2-4-2023\AutoPlay\Docs\GYM.accdb"
$backupFolder = "D:\Desktop\STYLE 2-4-2023\AutoPlay\Docs\Backups"
$timestamp = Get-Date -Format "yyyy-MM-dd_HH-mm"
$backupFile = "$backupFolder\GYM_Daily_$timestamp.accdb"

if (-not (Test-Path $backupFolder)) {
    New-Item -ItemType Directory -Path $backupFolder -Force
}

Copy-Item $sourceFile $backupFile -Force
Copy-Item $sourceFile "D:\Desktop\STYLE 2-4-2023\AutoPlay\Docs\GYM_Backup.accdb" -Force

# كتابة لوج
$logFile = "$backupFolder\backup_log.txt"
$logEntry = "07/26/2025 00:51:26: تم إنشاء نسخة احتياطية - $backupFile"
Add-Content -Path $logFile -Value $logEntry
