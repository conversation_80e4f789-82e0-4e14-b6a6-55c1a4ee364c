@echo off
echo ========================================
echo        نسخ احتياطي سريع - قاعدة بيانات الجيم
echo ========================================
echo.

REM إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجود
if not exist "Backups" mkdir Backups

REM إنشاء اسم الملف بالتاريخ والوقت
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "timestamp=%YYYY%-%MM%-%DD%_%HH%-%Min%"

REM نسخ الملف الأساسي
echo جاري إنشاء نسخة احتياطية...
copy "GYM.accdb" "Backups\GYM_Backup_%timestamp%.accdb" >nul
if %errorlevel% equ 0 (
    echo ✓ تم إنشاء النسخة الاحتياطية بنجاح
    echo   الملف: Backups\GYM_Backup_%timestamp%.accdb
) else (
    echo ✗ فشل في إنشاء النسخة الاحتياطية
    timeout /t 5 >nul
    exit /b 1
)

REM تحديث النسخة الاحتياطية الرئيسية
copy "GYM.accdb" "GYM_Backup.accdb" >nul
if %errorlevel% equ 0 (
    echo ✓ تم تحديث النسخة الاحتياطية الرئيسية
) else (
    echo ✗ فشل في تحديث النسخة الاحتياطية الرئيسية
)

REM كتابة سجل العمليات
echo %date% %time% - تم إنشاء نسخة احتياطية: GYM_Backup_%timestamp%.accdb >> Backups\backup_log.txt

echo.
echo ✅ تم إكمال النسخ الاحتياطي بنجاح
echo 🔄 سيتم الإغلاق تلقائياً خلال 3 ثوان...

REM انتظار 3 ثوان ثم إغلاق تلقائي
timeout /t 3 >nul
